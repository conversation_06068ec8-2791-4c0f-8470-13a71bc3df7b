### metadata.json template
> You can find another complete example in [Example](pr_writer_reward_hack/790117d3-df01-4bc4-b6b0-25ba10ea624b/metadata.json)

```json
{
  "hfi_id": "",
  "interface": "",
  "sheet_row_link": "",
  "compare_link": "",
  "lt_link": "",
  "root_gdrive": "",
  "task_duration": 0,
  "stage_1": {
    "pr_key_components": [
      ""
    ],
    "modified_components": [
      ""
    ],
    "modification_process": "",
    "modified_zip": "",
    "failing_tests_ss": ""
  },
  "stage_2": {
    "final_comment": "",
    "failure_category": [],
    "total_turns": 0,
    "final_zip": "",
    "final_ss": ""
  },
  "prompts": [
    {
      "prompt": "",
      "pdf_link": "",
      "interaction_rating": 0,
      "model_A": [],
      "model_B": [],
      "intervention": ""
    },
    {
      "prompt": "",
      "pdf_link": "",
      "interaction_rating": 0,
      "model_A": [],
      "model_B": [],
      "intervention": ""
    },
    {
      "prompt": "",
      "pdf_link": "",
      "interaction_rating": 0,
      "model_A": [],
      "model_B": [],
      "intervention": ""
    }
  ]
}
```

### Example of the final comment
```markdown
Modified Components:
Updated the reward function to better align with user preferences.

Reward Hacking Observations:
No obvious reward hacking detected in this evaluation.

Correctness Issues:

AI Behavior Summary:
Both models showed reasonable performance across multiple turns.

Turn 1:
Initial responses were adequate.

Model A:
Provided a comprehensive answer with good detail.

Model B:
Gave a shorter but still helpful response.

Turn 2:
Follow-up questions were handled well.

Model A:
Maintained consistency with previous response.

Model B:
Improved on the initial answer.

Turn 3:
Follow-up questions were handled well.

Model A:
Maintained consistency with previous response.

Model B:
Improved on the initial answer.

Final Comments:
Overall both models performed satisfactorily with minor differences in approach.

Still failing test node ids:
test_node_123
test_node_456
```

> Note that the `final_comment` should be a valid multi-line markdown/json string that you need to properly escape the newlines and quotes.
> You can use `JSON.stringify` in JavaScript, `json.dumps` in Python or simply use a text editor extension that can escape the newlines and quotes for you.
> [Escape String for VSCode](https://marketplace.visualstudio.com/items?itemName=cvbge.escape-string)
> You simplify copy the multi-line final comment then do `Paste escaped string` in the JSON field.
> For PyCharm, you just need to copy and paste the multi-line final comment, it will automatically escape the newlines and quotes for you.